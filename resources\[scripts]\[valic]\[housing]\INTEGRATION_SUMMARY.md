# Valic Housing Integration Summary

Tento dokument popisuje všechny integrace a exporty, kter<PERSON> byly přid<PERSON>y do valic_housing systému pro nap<PERSON>rování s ox_inventory a ox_doorlock.

## 🚪 ox_doorlock Integrace

### Nové Server Exporty

#### `updateDoorPasscode(doorId, passcode)`
- **Popis**: Dynamicky aktualizuje heslo konkrétních dveří bez nutnosti restartu
- **Parametry**: 
  - `doorId` (number): ID dveří v databázi
  - `passcode` (string): Nové heslo
- **Návrat**: boolean - true pokud byla aktualizace úspěšná

#### `refreshDoor(doorId)`
- **Popis**: Znovu načte všechna data konkrétních dveří z databáze
- **Parametry**: 
  - `doorId` (number): ID dveří v databázi
- **Návrat**: boolean - true pokud bylo načten<PERSON> ú<PERSON>

### Nové Client Eventy

#### `ox_doorlock:updateDoorPasscode`
- **Popis**: Aktualizuje heslo dveří na klientské straně
- **Parametry**: 
  - `doorId` (number): ID dveří
  - `passcode` (string): Nové heslo

#### `ox_doorlock:refreshDoor`
- **Popis**: Aktualizuje všechna data dveří na klientské straně
- **Parametry**: 
  - `doorId` (number): ID dveří
  - `data` (table): Nová data dveří

### Použití v valic_housing

Valic_housing nyní používá nové ox_doorlock exporty pro dynamické aktualizace hesel:

```lua
-- Místo přímé aktualizace databáze
if exports.ox_doorlock.updateDoorPasscode then
    exports.ox_doorlock.updateDoorPasscode(doorId, newPassword)
else
    -- Záložní řešení pro starší verze
    -- Přímá aktualizace databáze + TriggerEvent
end
```

## 📦 ox_inventory Integrace

### Nový Modul: `modules/valic_housing/`

#### Server Exporty

##### `createHouseStorage(houseId, slots, weight)`
- **Popis**: Vytvoří storage inventář pro dům
- **Parametry**: 
  - `houseId` (number): ID domu
  - `slots` (number, optional): Počet slotů (default: 50)
  - `weight` (number, optional): Maximální váha (default: 100000)
- **Návrat**: boolean

##### `openHouseStorage(playerId, houseId, slots, weight)`
- **Popis**: Otevře house storage pro hráče (s kontrolou přístupu)
- **Parametry**: 
  - `playerId` (number): ID hráče
  - `houseId` (number): ID domu
  - `slots` (number, optional): Počet slotů
  - `weight` (number, optional): Maximální váha
- **Návrat**: boolean, string (success, message)

##### `getHouseStorage(houseId)`
- **Popis**: Získá house storage inventář
- **Parametry**: 
  - `houseId` (number): ID domu
- **Návrat**: OxInventory | nil

##### `clearHouseStorage(houseId)`
- **Popis**: Vyčistí house storage (při prodeji/resetu)
- **Parametry**: 
  - `houseId` (number): ID domu
- **Návrat**: boolean

##### `canAccessHouseStorage(playerId, houseId)`
- **Popis**: Zkontroluje, zda hráč může přistupovat k house storage
- **Parametry**: 
  - `playerId` (number): ID hráče
  - `houseId` (number): ID domu
- **Návrat**: boolean

#### Client Exporty

##### `openHouseStorage(houseId, slots, weight)`
- **Popis**: Otevře house storage z klientské strany
- **Parametry**: 
  - `houseId` (number): ID domu
  - `slots` (number, optional): Počet slotů
  - `weight` (number, optional): Maximální váha
- **Návrat**: boolean

### Callback

#### `valic_housing:openStorage`
- **Popis**: Server callback pro otevření house storage
- **Parametry**: 
  - `houseId` (number): ID domu
  - `slots` (number, optional): Počet slotů
  - `weight` (number, optional): Maximální váha
- **Návrat**: boolean, string (success, message)

### Automatické Event Handling

Modul automaticky naslouchá těmto eventům:
- `valic_housing:server:HouseSold` - vyčistí storage při prodeji
- `valic_housing:server:HouseReset` - vyčistí storage při resetu
- `valic_housing:server:AllHousesReset` - vyčistí všechny storage při globálním resetu

### Použití v valic_housing

Do admin panelu byla přidána nová možnost "House Storage":

```lua
-- Pro vlastníky i držitele klíčů
table.insert(options, {
    title = 'House Storage',
    description = 'Access house storage',
    icon = 'box',
    onSelect = function()
        if exports.ox_inventory and exports.ox_inventory.openHouseStorage then
            exports.ox_inventory.openHouseStorage(houseId)
        end
    end
})
```

## 🏠 valic_housing Nové Exporty

### Server Exporty

```lua
exports('IsHouseOwner', function(houseId, citizenId))
exports('HasHouseKeys', function(houseId, citizenId))
exports('GetHouseById', function(houseId))
exports('GetAllHouses', function())
exports('GetHousesByCitizenId', function(citizenId))
exports('GetHousesWithKeys', function(citizenId))
exports('CanAccessHouse', function(houseId, citizenId, tourCode))
exports('IsTourValid', function(houseId, tourCode))
```

### Client Exporty

```lua
exports('checkDoorAccess', function(doorId))
```

## 🔧 Instalace a Konfigurace

### 1. Závislosti

Ujistěte se, že máte tyto resources:
- `qb-core` nebo `qbx_core`
- `ox_lib`
- `ox_inventory`
- `ox_doorlock`
- `valic_housing`

### 2. Start Order

V `server.cfg`:
```
ensure qb-core
ensure ox_lib
ensure ox_doorlock
ensure ox_inventory
ensure valic_housing
```

### 3. Konfigurace ox_doorlock

V konfiguraci dveří přidejte:
```lua
lockpick = 'valic_housing.checkDoorAccess'
```

### 4. Automatické načítání

Integrace se načítají automaticky při startu, pokud jsou všechny resources dostupné.

## 🎯 Výhody Integrace

### ox_doorlock
- ✅ Dynamické aktualizace hesel bez restartu
- ✅ Okamžitá synchronizace na všech klientech
- ✅ Zpětná kompatibilita se starými verzemi
- ✅ Izolované aktualizace pouze konkrétních dveří

### ox_inventory
- ✅ Automatické storage pro každý dům
- ✅ Kontrola přístupu na základě vlastnictví/klíčů
- ✅ Automatické čištění při prodeji/resetu
- ✅ Integrace do admin panelu
- ✅ Logování transakcí

### valic_housing
- ✅ Kompletní sada exportů pro jiné resources
- ✅ Rozšířená funkcionalita bez změny core logiky
- ✅ Modulární design pro snadné rozšíření

## 🐛 Troubleshooting

### Časté problémy

1. **Storage se neotevírá**
   - Zkontrolujte, zda je ox_inventory spuštěn
   - Ověřte, že hráč má přístup k domu

2. **Hesla se neaktualizují**
   - Zkontrolujte, zda je ox_doorlock spuštěn
   - Ověřte správné ID dveří v konfiguraci

3. **Exporty nefungují**
   - Zkontrolujte start order resources
   - Ověřte, že všechny dependencies jsou splněny

### Debug

Pro debugging zapněte:
```lua
Config.Debug = true
```

V `server/config.lua` valic_housing.
