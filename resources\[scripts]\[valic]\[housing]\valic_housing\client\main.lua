-- Main client-side functionality for valic_housing

local QBCore = exports['qb-core']:GetCoreObject()

-- Load config
if not Config then
    Config = {}
    Config.Debug = false
    Config.Locale = 'cs'
    Config.MinPasswordLength = 4
    Config.MaxPasswordLength = 12
    Config.MaxPasswordAttempts = 3
    Config.PasswordLockoutTime = 10
    Config.TourDuration = 10
    Config.TourCodeLength = 4

    -- Load houses config
    Config.Houses = {}
end

-- Define a safe locale function that doesn't rely on the global _ function
function GetLocaleString(key, ...)
    -- Define common translations
    local translations = {
        ['notification_title'] = 'Valic Housing',
        ['enter_password'] = '<PERSON>ade<PERSON><PERSON> heslo',
        ['incorrect_password'] = 'Nesprávné heslo',
        ['password_attempts_remaining'] = 'Zbývající pokusy: %d',
        ['door_locked'] = '<PERSON><PERSON><PERSON><PERSON> jsou u<PERSON> na %d minut',
        ['tour_time_remaining'] = 'Zbývají<PERSON><PERSON> čas prohlídky: %d minut',
        ['tour_code'] = 'Kód prohlídky: %s',
        ['property_for_sale'] = 'Nemovitost na prodej',
        ['property_price'] = 'Cena: $%s',
        ['property_name'] = 'Název: %s',
        ['buy_property'] = 'Koupit nemovitost',
        ['sell_property'] = 'Prodat nemovitost',
        ['transfer_property'] = 'Přepsat nemovitost',
        ['rename_property'] = 'Přejmenovat nemovitost',
        ['admin_panel'] = 'Administrátorský panel',
        ['admin_panel_owner'] = 'Administrátorský panel - Majitel',
        ['admin_panel_key_holder'] = 'Administrátorský panel - Držitel klíče',
        ['change_password'] = 'Změnit heslo',
        ['current_password'] = 'Aktuální heslo',
        ['new_password'] = 'Nové heslo',
        ['confirm_password'] = 'Potvrdit heslo',
        ['password_mismatch'] = 'Hesla se neshodují',
        ['invalid_password'] = 'Neplatné heslo',
        ['password_requirements'] = 'Heslo musí mít %d-%d alfanumerických znaků',
        ['share_keys'] = 'Sdílet klíče',
        ['revoke_keys'] = 'Odebrat klíče',
        ['no_players_nearby'] = 'Žádní hráči poblíž',
        ['request_tour'] = 'Požádat o prohlídku'
    }

    local translation = translations[key]

    if not translation then
        return 'Missing translation: ' .. key
    end

    if ... then
        return string.format(translation, ...)
    end

    return translation
end

-- For backward compatibility, define _ as a global function
function _(key, ...)
    return GetLocaleString(key, ...)
end

-- Local variables
local houses = {}
local npcs = {}
local blips = {}
local adminPanelZones = {}
local activeHouseId = nil
local activeTourCode = nil

-- Initialize
local function Initialize()
    QBCore.Functions.TriggerCallback('valic_housing:server:GetAllHouses', function(serverHouses)
        houses = serverHouses
        RefreshHouses()
    end)
end

-- Refresh houses (NPCs, blips, zones)
function RefreshHouses()
    -- Clear existing NPCs, blips, and zones
    ClearNPCs()
    ClearBlips()
    ClearAdminPanelZones()

    -- Get player data
    local PlayerData = QBCore.Functions.GetPlayerData()
    local citizenId = PlayerData.citizenid

    -- Get owned houses and houses with keys
    QBCore.Functions.TriggerCallback('valic_housing:server:GetOwnedHouses', function(ownedHouses)
        QBCore.Functions.TriggerCallback('valic_housing:server:GetHousesWithKeys', function(housesWithKeys)
            -- Process all houses
            for _, house in pairs(houses) do
                local isOwner = false
                local hasKeys = false

                -- Check if player owns this house
                for _, ownedHouse in pairs(ownedHouses) do
                    if ownedHouse.id == house.id then
                        isOwner = true
                        break
                    end
                end

                -- Check if player has keys to this house
                for _, keyHouse in pairs(housesWithKeys) do
                    if keyHouse.id == house.id then
                        hasKeys = true
                        break
                    end
                end

                -- Create appropriate elements based on ownership status
                if isOwner then
                    -- Create admin panel zone and owned property blip
                    CreateAdminPanelZone(house.id)
                    CreateBlip(house.id, 'owned', house.property_name)
                elseif hasKeys then
                    -- Create shared property blip
                    CreateBlip(house.id, 'shared', house.property_name)
                else
                    -- Create NPC and available property blip if not owned
                    if not house.owner_citizenid then
                        CreateNPC(house.id)
                        CreateBlip(house.id, 'available', house.property_name)
                    end
                end
            end
        end)
    end)
end

-- Create NPC for a house
function CreateNPC(houseId)
    -- Find house in config
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if not houseConfig then return end

    -- Create NPC
    local model = Config.NPCModel
    local coords = houseConfig.npc.coords
    local scenario = houseConfig.npc.scenario or Config.NPCScenario

    RequestModel(GetHashKey(model))
    while not HasModelLoaded(GetHashKey(model)) do
        Wait(1)
    end

    local npc = CreatePed(4, GetHashKey(model), coords.x, coords.y, coords.z - 1.0, coords.w, false, true)
    SetEntityHeading(npc, coords.w)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)

    if scenario then
        TaskStartScenarioInPlace(npc, scenario, 0, true)
    end

    npcs[houseId] = npc

    -- Create target zone for NPC
    exports.ox_target:addLocalEntity(npc, {
        {
            name = 'valic_housing:npc:' .. houseId,
            icon = 'fas fa-home',
            label = _('property_for_sale'),
            distance = 2.0,
            onSelect = function()
                OpenPropertyMenu(houseId)
            end
        }
    })
end

-- Clear all NPCs
function ClearNPCs()
    for houseId, npc in pairs(npcs) do
        if DoesEntityExist(npc) then
            exports.ox_target:removeLocalEntity(npc)
            DeleteEntity(npc)
        end
    end
    npcs = {}
end

-- Create blip for a house
function CreateBlip(houseId, blipType, name)
    -- Find house in config
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if not houseConfig then return end

    -- Get blip settings based on blipType
    local blipSettings = nil
    if blipType == 'owned' then
        blipSettings = Config.Blips.OwnedProperty
    elseif blipType == 'shared' then
        blipSettings = Config.Blips.SharedProperty
    else -- available
        blipSettings = Config.Blips.AvailableProperty
    end

    -- Create blip
    local x, y, z

    if blipType == 'owned' or blipType == 'shared' then
        if houseConfig.adminPanel and houseConfig.adminPanel.coords then
            local coordType = type(houseConfig.adminPanel.coords)
            if coordType == 'vector3' then
                x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
            elseif coordType == 'vector4' then
                x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
            elseif coordType == 'table' then
                x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
            else
                print("^1[ERROR]^7 Invalid adminPanel coords format for house ID: " .. houseId)
                return
            end
        else
            print("^1[ERROR]^7 Missing adminPanel coords for house ID: " .. houseId)
            return
        end
    else
        if houseConfig.npc and houseConfig.npc.coords then
            local coordType = type(houseConfig.npc.coords)
            if coordType == 'vector3' then
                x, y, z = houseConfig.npc.coords.x, houseConfig.npc.coords.y, houseConfig.npc.coords.z
            elseif coordType == 'vector4' then
                x, y, z = houseConfig.npc.coords.x, houseConfig.npc.coords.y, houseConfig.npc.coords.z
            elseif coordType == 'table' then
                x, y, z = houseConfig.npc.coords.x, houseConfig.npc.coords.y, houseConfig.npc.coords.z
            else
                print("^1[ERROR]^7 Invalid npc coords format for house ID: " .. houseId)
                return
            end
        else
            print("^1[ERROR]^7 Missing npc coords for house ID: " .. houseId)
            return
        end
    end

    local blip = AddBlipForCoord(x, y, z)
    SetBlipSprite(blip, blipSettings.Sprite)
    SetBlipDisplay(blip, blipSettings.Display)
    SetBlipScale(blip, blipSettings.Scale)
    SetBlipColour(blip, blipSettings.Color)
    SetBlipAsShortRange(blip, blipSettings.ShortRange)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(name)
    EndTextCommandSetBlipName(blip)

    blips[houseId] = blip
end

-- Clear all blips
function ClearBlips()
    for houseId, blip in pairs(blips) do
        if DoesBlipExist(blip) then
            RemoveBlip(blip)
        end
    end
    blips = {}
end

-- Create admin panel zone for a house
function CreateAdminPanelZone(houseId)
    -- Check if zone already exists for this house
    if adminPanelZones[houseId] then
        -- Zone already exists, no need to create a new one
        return
    end

    -- Find house in config
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if not houseConfig then return end

    -- Create target zone
    local x, y, z

    if houseConfig.adminPanel and houseConfig.adminPanel.coords then
        local coordType = type(houseConfig.adminPanel.coords)
        if coordType == 'vector3' then
            x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
        elseif coordType == 'vector4' then
            x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
        elseif coordType == 'table' then
            x, y, z = houseConfig.adminPanel.coords.x, houseConfig.adminPanel.coords.y, houseConfig.adminPanel.coords.z
        else
            print("^1[ERROR]^7 Invalid adminPanel coords format for house ID: " .. houseId)
            return
        end
    else
        print("^1[ERROR]^7 Missing adminPanel coords for house ID: " .. houseId)
        return
    end

    local zone = exports.ox_target:addSphereZone({
        coords = vec3(x, y, z),
        radius = 1.0,
        debug = Config.Debug,
        options = {
            {
                name = 'valic_housing:adminPanel:' .. houseId,
                icon = 'fas fa-cog',
                label = _('admin_panel'),
                distance = 2.0,
                onSelect = function()
                    OpenAdminPanel(houseId)
                end
            }
        }
    })

    adminPanelZones[houseId] = zone
end

-- Clear all admin panel zones
function ClearAdminPanelZones()
    for houseId, zone in pairs(adminPanelZones) do
        exports.ox_target:removeZone(zone)
    end
    adminPanelZones = {}
end

-- Open property menu (for unowned houses)
function OpenPropertyMenu(houseId)
    -- Find house in database
    local house = nil
    for _, h in pairs(houses) do
        if h.id == houseId then
            house = h
            break
        end
    end

    if not house then return end

    -- Find house in config
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if not houseConfig then return end

    -- Create menu options
    local options = {
        {
            title = _('property_name', house.property_name),
            description = "Adresa: " .. GetStreetNameFromHashKey(GetStreetNameAtCoord(houseConfig.npc.coords.x, houseConfig.npc.coords.y, houseConfig.npc.coords.z)),
            icon = 'home'
        }
    }

    -- Check if player has an active tour for this house
    if activeHouseId == houseId and activeTourCode then
        -- Add option to end tour
        table.insert(options, {
            title = 'Ukončit prohlídku',
            description = 'Ukončí prohlídku nemovitosti a změní heslo dveří',
            icon = 'door-closed',
            onSelect = function()
                -- Confirm end tour
                local confirm = lib.alertDialog({
                    header = 'Ukončit prohlídku',
                    content = 'Opravdu chcete ukončit prohlídku? Dveře budou uzamčeny a heslo bude změněno.',
                    centered = true,
                    cancel = true,
                    labels = {
                        confirm = 'Ano, ukončit prohlídku',
                        cancel = 'Ne, pokračovat v prohlídce'
                    }
                })

                if confirm == 'confirm' then
                    TriggerServerEvent('valic_housing:server:EndTour', houseId)
                    activeHouseId = nil
                    activeTourCode = nil
                    lib.hideTextUI()
                    lib.notify({
                        title = 'Prohlídka ukončena',
                        description = 'Prohlídka byla úspěšně ukončena',
                        type = 'success'
                    })
                end
            end
        })
    else
        -- Add options for buying or starting a tour
        table.insert(options, {
            title = _('buy_property'),
            description = _('property_price', houseConfig.price),
            icon = 'money-bill',
            onSelect = function()
                OpenPasswordSetupMenu(houseId)
            end
        })

        table.insert(options, {
            title = _('request_tour'),
            description = _('tour_time_remaining', Config.TourDuration),
            icon = 'eye',
            onSelect = function()
                TriggerServerEvent('valic_housing:server:StartTour', houseId)
            end
        })

    end

    -- Show menu
    lib.registerContext({
        id = 'valic_housing:propertyMenu:' .. houseId,
        title = _('property_for_sale'),
        options = options
    })

    lib.showContext('valic_housing:propertyMenu:' .. houseId)
end

-- Open password setup menu (when buying a house)
function OpenPasswordSetupMenu(houseId)
    lib.registerContext({
        id = 'valic_housing:passwordSetup:' .. houseId,
        title = _('new_password'),
        options = {
            {
                title = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
                description = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
                icon = 'info-circle'
            }
        }
    })

    lib.showContext('valic_housing:passwordSetup:' .. houseId)

    local input = lib.inputDialog(_('new_password'), {
        {
            type = 'input',
            label = _('new_password'),
            password = true,
            icon = 'lock',
            required = true,
            min = Config.MinPasswordLength,
            max = Config.MaxPasswordLength
        },
        {
            type = 'input',
            label = _('confirm_password'),
            password = true,
            icon = 'lock',
            required = true,
            min = Config.MinPasswordLength,
            max = Config.MaxPasswordLength
        }
    })

    if not input then return end

    local password = input[1]
    local confirmPassword = input[2]

    if password ~= confirmPassword then
        lib.notify({
            title = _('notification_title'),
            description = _('password_mismatch'),
            type = 'error'
        })
        return
    end

    if not IsValidPassword(password) then
        lib.notify({
            title = _('notification_title'),
            description = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
            type = 'error'
        })
        return
    end

    -- Purchase house
    TriggerServerEvent('valic_housing:server:PurchaseHouse', houseId, password)
end

-- Open admin panel (for owned houses)
function OpenAdminPanel(houseId)
    -- Check if player can access this house
    QBCore.Functions.TriggerCallback('valic_housing:server:CanAccessHouse', function(canAccess, accessType)
        if not canAccess then
            lib.notify({
                title = _('notification_title'),
                description = 'You do not have access to this property',
                type = 'error'
            })
            return
        end

        -- Find house in database
        local house = nil
        for _, h in pairs(houses) do
            if h.id == houseId then
                house = h
                break
            end
        end

        if not house then return end

        -- Create menu options based on access type
        local options = {}

        -- Common options for both owner and key holder
        table.insert(options, {
            title = _('property_name', house.property_name),
            description = house.owner_citizenid and 'Owner: ' .. house.owner_citizenid or '',
            icon = 'home'
        })

        table.insert(options, {
            title = _('change_password'),
            description = _('current_password'),
            icon = 'lock',
            onSelect = function()
                OpenChangePasswordMenu(houseId)
            end
        })

        -- Add storage option if ox_inventory is available (for both owners and key holders)
        if GetResourceState('ox_inventory') == 'started' then
            table.insert(options, {
                title = 'House Storage',
                description = 'Access house storage',
                icon = 'box',
                onSelect = function()
                    if exports.ox_inventory and exports.ox_inventory.openHouseStorage then
                        exports.ox_inventory.openHouseStorage(houseId)
                    else
                        lib.notify({
                            title = 'Storage',
                            description = 'Storage system not available',
                            type = 'error'
                        })
                    end
                end
            })
        end

        -- Owner-only options
        if accessType == 'owner' then
            table.insert(options, {
                title = _('rename_property'),
                description = _('property_name', house.property_name),
                icon = 'edit',
                onSelect = function()
                    OpenRenamePropertyMenu(houseId)
                end
            })

            table.insert(options, {
                title = _('share_keys'),
                description = _('select_player'),
                icon = 'key',
                onSelect = function()
                    OpenShareKeysMenu(houseId)
                end
            })

            -- Only show revoke keys if there are key holders
            if house.key_holders and house.key_holders ~= '[]' and house.key_holders ~= 'null' then
                table.insert(options, {
                    title = _('revoke_keys'),
                    description = _('select_player'),
                    icon = 'key-slash',
                    onSelect = function()
                        OpenRevokeKeysMenu(houseId)
                    end
                })
            end

            table.insert(options, {
                title = _('transfer_property'),
                description = _('select_player'),
                icon = 'exchange-alt',
                onSelect = function()
                    OpenTransferPropertyMenu(houseId)
                end
            })

            table.insert(options, {
                title = _('sell_property'),
                description = 'Sell for 80% of purchase price',
                icon = 'dollar-sign',
                onSelect = function()
                    OpenSellPropertyMenu(houseId)
                end
            })
        end

        -- Add storage option if ox_inventory is available
        if GetResourceState('ox_inventory') == 'started' then
            table.insert(options, {
                title = 'House Storage',
                description = 'Access your house storage',
                icon = 'box',
                onSelect = function()
                    if exports.ox_inventory and exports.ox_inventory.openHouseStorage then
                        exports.ox_inventory.openHouseStorage(houseId)
                    else
                        lib.notify({
                            title = 'Storage',
                            description = 'Storage system not available',
                            type = 'error'
                        })
                    end
                end
            })
        end

        -- Show menu
        lib.registerContext({
            id = 'valic_housing:adminPanel:' .. houseId,
            title = accessType == 'owner' and _('admin_panel_owner') or _('admin_panel_key_holder'),
            options = options
        })

        lib.showContext('valic_housing:adminPanel:' .. houseId)
    end, houseId)
end

-- Open change password menu
function OpenChangePasswordMenu(houseId)
    local input = lib.inputDialog(_('change_password'), {
        {
            type = 'input',
            label = _('current_password'),
            password = true,
            icon = 'lock',
            required = true
        },
        {
            type = 'input',
            label = _('new_password'),
            password = true,
            icon = 'lock',
            required = true,
            min = Config.MinPasswordLength,
            max = Config.MaxPasswordLength
        },
        {
            type = 'input',
            label = _('confirm_password'),
            password = true,
            icon = 'lock',
            required = true,
            min = Config.MinPasswordLength,
            max = Config.MaxPasswordLength
        }
    })

    if not input then return end

    local currentPassword = input[1]
    local newPassword = input[2]
    local confirmPassword = input[3]

    -- Check if passwords match
    if newPassword ~= confirmPassword then
        lib.notify({
            title = _('notification_title'),
            description = _('password_mismatch'),
            type = 'error'
        })
        return
    end

    -- Check if new password is valid
    if not IsValidPassword(newPassword) then
        lib.notify({
            title = _('notification_title'),
            description = _('password_requirements', Config.MinPasswordLength, Config.MaxPasswordLength),
            type = 'error'
        })
        return
    end

    -- Verify current password
    QBCore.Functions.TriggerCallback('valic_housing:server:CheckPassword', function(success, reason, data)
        if not success then
            if reason == 'locked' then
                lib.notify({
                    title = _('notification_title'),
                    description = _('door_locked', data),
                    type = 'error'
                })
            elseif reason == 'incorrect' then
                lib.notify({
                    title = _('notification_title'),
                    description = _('incorrect_password') .. ' ' .. _('password_attempts_remaining', data),
                    type = 'error'
                })
            else
                lib.notify({
                    title = _('notification_title'),
                    description = _('invalid_password'),
                    type = 'error'
                })
            end
            return
        end

        -- Change password
        TriggerServerEvent('valic_housing:server:ChangePassword', houseId, newPassword)
    end, houseId, currentPassword)
end

-- Open rename property menu
function OpenRenamePropertyMenu(houseId)
    -- Find house in database
    local house = nil
    for _, h in pairs(houses) do
        if h.id == houseId then
            house = h
            break
        end
    end

    if not house then return end

    local input = lib.inputDialog(_('rename_property'), {
        {
            type = 'input',
            label = _('property_name', ''),
            default = house.property_name,
            icon = 'home',
            required = true,
            min = 1,
            max = 100
        }
    })

    if not input then return end

    local newName = input[1]

    if not newName or newName == '' then
        lib.notify({
            title = _('notification_title'),
            description = 'Invalid property name',
            type = 'error'
        })
        return
    end

    -- Rename property
    TriggerServerEvent('valic_housing:server:RenameHouse', houseId, newName)
end

-- Open share keys menu
function OpenShareKeysMenu(houseId)
    -- Get nearby players
    QBCore.Functions.TriggerCallback('valic_housing:server:GetNearbyPlayers', function(nearbyPlayers)
        if #nearbyPlayers == 0 then
            lib.notify({
                title = _('notification_title'),
                description = _('no_players_nearby'),
                type = 'error'
            })
            return
        end

        -- Create menu options
        local options = {}

        for _, player in ipairs(nearbyPlayers) do
            table.insert(options, {
                title = player.name,
                description = 'ID: ' .. player.id,
                icon = 'user',
                onSelect = function()
                    TriggerServerEvent('valic_housing:server:ShareKeys', houseId, player.id)
                end
            })
        end

        -- Show menu
        lib.registerContext({
            id = 'valic_housing:shareKeys:' .. houseId,
            title = _('share_keys'),
            options = options
        })

        lib.showContext('valic_housing:shareKeys:' .. houseId)
    end)
end

-- Open revoke keys menu
function OpenRevokeKeysMenu(houseId)
    -- Find house in database
    local house = nil
    for _, h in pairs(houses) do
        if h.id == houseId then
            house = h
            break
        end
    end

    if not house then return end

    -- Check if there are key holders
    if not house.key_holders or house.key_holders == '[]' or house.key_holders == 'null' then
        lib.notify({
            title = _('notification_title'),
            description = 'No key holders found',
            type = 'error'
        })
        return
    end

    -- Parse key holders
    local keyHolders = json.decode(house.key_holders) or {}

    if #keyHolders == 0 then
        lib.notify({
            title = _('notification_title'),
            description = 'No key holders found',
            type = 'error'
        })
        return
    end

    -- Get player names for key holders
    local options = {}

    for _, citizenId in ipairs(keyHolders) do
        table.insert(options, {
            title = 'Odebrat klíče',
            description = 'Citizen ID: ' .. citizenId,
            icon = 'key',
            onSelect = function()
                TriggerServerEvent('valic_housing:server:RevokeKeys', houseId, citizenId)
            end
        })
    end

    -- Show menu
    lib.registerContext({
        id = 'valic_housing:revokeKeys:' .. houseId,
        title = _('revoke_keys'),
        options = options
    })

    lib.showContext('valic_housing:revokeKeys:' .. houseId)
end

-- Open transfer property menu
function OpenTransferPropertyMenu(houseId)
    -- Get nearby players
    QBCore.Functions.TriggerCallback('valic_housing:server:GetNearbyPlayers', function(nearbyPlayers)
        if #nearbyPlayers == 0 then
            lib.notify({
                title = _('notification_title'),
                description = _('no_players_nearby'),
                type = 'error'
            })
            return
        end

        -- Create menu options
        local options = {}

        for _, player in ipairs(nearbyPlayers) do
            table.insert(options, {
                title = player.name,
                description = 'ID: ' .. player.id,
                icon = 'user',
                onSelect = function()
                    -- Confirm transfer
                    local confirm = lib.alertDialog({
                        header = 'Přepsat nemovitost',
                        content = 'Opravdu chcete přepsat tuto nemovitost na ' .. player.name .. '?',
                        centered = true,
                        cancel = true
                    })

                    if confirm == 'confirm' then
                        TriggerServerEvent('valic_housing:server:TransferHouse', houseId, player.id)
                    end
                end
            })
        end

        -- Show menu
        lib.registerContext({
            id = 'valic_housing:transferProperty:' .. houseId,
            title = 'Přepsat nemovitost',
            options = options
        })

        lib.showContext('valic_housing:transferProperty:' .. houseId)
    end)
end

-- Open sell property menu
function OpenSellPropertyMenu(houseId)
    -- Find house in config
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if not houseConfig then return end

    -- Calculate sell price (80% of purchase price)
    local sellPrice = math.floor(houseConfig.price * 0.8)

    -- Confirm sale
    local confirm = lib.alertDialog({
        header = 'Prodat nemovitost',
        content = 'Opravdu chcete prodat tuto nemovitost za $' .. sellPrice .. '?',
        centered = true,
        cancel = true
    })

    if confirm == 'confirm' then
        TriggerServerEvent('valic_housing:server:SellHouse', houseId)
    end
end

-- Check door password
function CheckDoorPassword(doorId)
    -- Ensure doorId is a number
    doorId = tonumber(doorId)
    if not doorId then
        print("^1[ERROR]^7 Invalid door ID: " .. tostring(doorId))
        return
    end

    -- Find which house this door belongs to
    local houseId = nil

    for _, house in pairs(houses) do
        if house.door_ids then
            local doorIds = json.decode(house.door_ids) or {}
            for _, id in ipairs(doorIds) do
                -- Ensure id is a number for comparison
                id = tonumber(id)
                if id and id == doorId then
                    houseId = house.id
                    break
                end
            end
            if houseId then break end
        end
    end

    if not houseId then
        lib.notify({
            title = _('notification_title'),
            description = 'Door not found in any property',
            type = 'error'
        })
        return
    end

    -- Check if player can access this house
    QBCore.Functions.TriggerCallback('valic_housing:server:CanAccessHouse', function(canAccess, accessType)
        if canAccess then
            -- If player is owner or has keys, unlock door directly
            exports.ox_doorlock:setDoorState(doorId, 0) -- 0 = unlocked
            return
        end

        -- Check if house is locked due to too many attempts
        if IsHouseLocked(houseId) then
            local remainingTime = math.ceil(GetLockoutTimeRemaining(houseId) / 60)
            lib.notify({
                title = _('notification_title'),
                description = _('door_locked', remainingTime),
                type = 'error'
            })
            return
        end

        -- If player has active tour code, check it
        if activeTourCode and activeHouseId == houseId then
            QBCore.Functions.TriggerCallback('valic_housing:server:IsTourValid', function(isValid)
                if isValid then
                    exports.ox_doorlock:setDoorState(doorId, 0) -- 0 = unlocked
                else
                    lib.notify({
                        title = _('notification_title'),
                        description = 'Tour code expired',
                        type = 'error'
                    })
                    activeTourCode = nil
                    activeHouseId = nil
                end
            end, houseId, activeTourCode)
            return
        end

        -- Otherwise, prompt for password
        local input = lib.inputDialog(_('enter_password'), {
            {
                type = 'input',
                label = _('enter_password'),
                password = true,
                icon = 'lock',
                required = true
            }
        })

        if not input then return end

        local password = input[1]

        -- Check password
        QBCore.Functions.TriggerCallback('valic_housing:server:CheckPassword', function(success, reason, data)
            if success then
                exports.ox_doorlock:setDoorState(doorId, 0) -- 0 = unlocked
            else
                if reason == 'locked' then
                    lib.notify({
                        title = _('notification_title'),
                        description = _('door_locked', data),
                        type = 'error'
                    })
                elseif reason == 'incorrect' then
                    lib.notify({
                        title = _('notification_title'),
                        description = _('incorrect_password') .. ' ' .. _('password_attempts_remaining', data),
                        type = 'error'
                    })

                    -- Play error sound
                    PlaySoundFrontend(-1, "Place_Prop_Fail", "DLC_Dmod_Prop_Editor_Sounds", 0)

                    -- If last attempt, play alarm
                    if data == 1 then
                        PlaySoundFrontend(-1, "Beep_Red", "DLC_HEIST_HACKING_SNAKE_SOUNDS", 1)
                    end
                else
                    lib.notify({
                        title = _('notification_title'),
                        description = _('invalid_password'),
                        type = 'error'
                    })
                end
            end
        end, houseId, password)
    end, houseId)
end

-- Start property tour
function StartTour(houseId, tourCode, remainingMinutes)
    activeHouseId = houseId
    activeTourCode = tourCode

    -- Use remaining minutes provided by server, or default to Config.TourDuration
    remainingMinutes = remainingMinutes or Config.TourDuration

    -- Show tour code on screen as static text that updates every minute
    Citizen.CreateThread(function()
        local endTime = os.time() + (remainingMinutes * 60) -- End time in Unix timestamp (seconds)
        local lastMinute = -1

        -- Vždy zobrazit notifikaci na začátku
        DisplayTourInfo(tourCode, remainingMinutes)

        -- Přidáme tlačítko pro ukončení prohlídky
        lib.registerContext({
            id = 'valic_housing:endTourMenu',
            title = 'Prohlídka nemovitosti',
            options = {
                {
                    title = 'Ukončit prohlídku',
                    description = 'Ukončí prohlídku nemovitosti a změní heslo dveří',
                    icon = 'door-closed',
                    onSelect = function()
                        -- Confirm end tour
                        local confirm = lib.alertDialog({
                            header = 'Ukončit prohlídku',
                            content = 'Opravdu chcete ukončit prohlídku? Dveře budou uzamčeny a heslo bude změněno.',
                            centered = true,
                            cancel = true,
                            labels = {
                                confirm = 'Ano, ukončit prohlídku',
                                cancel = 'Ne, pokračovat v prohlídce'
                            }
                        })

                        if confirm == 'confirm' then
                            TriggerServerEvent('valic_housing:server:EndTour', houseId)
                            activeHouseId = nil
                            activeTourCode = nil
                            lib.hideTextUI()
                            lib.notify({
                                title = 'Prohlídka ukončena',
                                description = 'Prohlídka byla úspěšně ukončena',
                                type = 'success'
                            })
                        end
                    end
                }
            }
        })

        -- Odstraněno - už nepotřebujeme command ani keymapping, protože ukončení prohlídky je dostupné přes NPC

        while os.time() < endTime and activeTourCode do
            -- Calculate remaining minutes
            local timeLeft = math.ceil((endTime - os.time()) / 60) -- Convert to minutes

            -- Only update the text when the minute changes
            if timeLeft ~= lastMinute then
                lastMinute = timeLeft

                -- Update the persistent notification
                DisplayTourInfo(tourCode, timeLeft)
            end

            Citizen.Wait(1000) -- Still check every second, but only update UI when minute changes
        end

        -- Hide the notification when tour ends
        lib.hideTextUI()
    end)

    -- Set a timer to end the tour after the remaining time
    Citizen.SetTimeout(remainingMinutes * 60 * 1000, function()
        if activeTourCode then
            TriggerServerEvent('valic_housing:server:EndTour', houseId)
            activeHouseId = nil
            activeTourCode = nil
        end
    end)
end

-- Note: Door passcode updates are now handled directly on the server side

-- Display tour information on screen
function DisplayTourInfo(tourCode, remainingMinutes)
    -- Zobrazit informace o prohlídce jako text na obrazovce
    lib.showTextUI("Kód prohlídky: " .. tourCode .. " (Zbývá: " .. remainingMinutes .. " min)", {
        position = "top-center",
        icon = "eye",
        style = {
            borderRadius = 5,
            backgroundColor = '#141517',
            color = 'white'
        }
    })
end

-- End property tour
function EndTour()
    -- Hide the text UI if it's showing
    lib.hideTextUI()

    -- Reset door passcode in ox_doorlock if we have an active house
    if activeHouseId then
        -- Store the active house ID in a local variable before resetting it
        local houseId = activeHouseId

        -- Reset the global variables first to prevent potential recursive issues
        activeHouseId = nil
        activeTourCode = nil

        -- Server will handle resetting the door passcodes
        TriggerServerEvent('valic_housing:server:EndTour', houseId)
    else
        -- Just reset the variables if no active house
        activeHouseId = nil
        activeTourCode = nil
    end
end

-- Event handlers

-- Initialize when player loads
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Initialize()
end)

-- Refresh houses when needed
RegisterNetEvent('valic_housing:client:RefreshHouses', function()
    QBCore.Functions.TriggerCallback('valic_housing:server:GetAllHouses', function(serverHouses)
        houses = serverHouses
        RefreshHouses()
    end)
end)

-- House purchased
RegisterNetEvent('valic_housing:client:HousePurchased', function(houseId)
    RefreshHouses()
end)

-- House sold
RegisterNetEvent('valic_housing:client:HouseSold', function(houseId)
    -- Najít dům v konfiguraci
    local houseConfig = nil
    for _, configHouse in pairs(Config.Houses) do
        if configHouse.id == houseId then
            houseConfig = configHouse
            break
        end
    end

    if houseConfig and houseConfig.npc and houseConfig.npc.coords then
        -- Teleportovat hráče k NPC
        SetEntityCoords(PlayerPedId(), houseConfig.npc.coords.x, houseConfig.npc.coords.y, houseConfig.npc.coords.z)
        SetEntityHeading(PlayerPedId(), houseConfig.npc.coords.w)
    end

    RefreshHouses()
end)

-- House transferred
RegisterNetEvent('valic_housing:client:HouseTransferred', function(houseId)
    RefreshHouses()
end)

-- House renamed
RegisterNetEvent('valic_housing:client:HouseRenamed', function(houseId, newName)
    -- Update local house data
    for i, house in pairs(houses) do
        if house.id == houseId then
            houses[i].property_name = newName
            break
        end
    end

    -- Update blip name if it exists
    if blips[houseId] and DoesBlipExist(blips[houseId]) then
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(newName)
        EndTextCommandSetBlipName(blips[houseId])
    end
end)

-- Keys shared
RegisterNetEvent('valic_housing:client:KeysShared', function(houseId)
    RefreshHouses()
end)

-- Keys revoked
RegisterNetEvent('valic_housing:client:KeysRevoked', function(houseId)
    RefreshHouses()
end)

-- Tour started
RegisterNetEvent('valic_housing:client:TourStarted', function(houseId, tourCode, remainingMinutes)
    StartTour(houseId, tourCode, remainingMinutes)
end)

-- Tour ended
RegisterNetEvent('valic_housing:client:TourEnded', function(houseId)
    EndTour()
end)

-- Confirm reset all houses
RegisterNetEvent('valic_housing:client:ConfirmResetAllHouses', function()
    -- Show confirmation dialog
    local confirm = lib.alertDialog({
        header = 'Reset všech domů',
        content = 'Opravdu chcete resetovat všechny domy? Tato akce odstraní všechny vlastníky, hesla a sdílené klíče. Tuto akci nelze vrátit!',
        centered = true,
        cancel = true,
        labels = {
            confirm = 'Ano, resetovat vše',
            cancel = 'Ne, zrušit'
        }
    })

    if confirm == 'confirm' then
        TriggerServerEvent('valic_housing:server:ResetAllHouses')
    end
end)

-- Initialize on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        Initialize()
    end
end)

-- Initialize when player loads
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Initialize()
end)

-- Note: Door access check is now handled in doors.lua